# CharacterDeckUI UxmlElement Fix

## Проблема
CharacterDeckUI был помечен атрибутом `[UxmlElement]` но отсутствовал атрибут `ExtensionOfNativeClass`, что вызывало ошибку компиляции.

## Решение
Исправлены следующие файлы:

### 1. Assets/Scripts/UI/Scripts/CharacterDeckUI.cs
- Класс правильно помечен атрибутом `[UxmlElement]`
- Наследуется от `VisualElement`
- Содержит всю логику управления персонажами

### 2. Assets/Scripts/UI/Source/Gameplay.uxml
- Обновлен для использования `<CharacterDeckUI>` вместо `<CharacterDeck>`
- Правильное имя элемента: `name="CharacterDeckUI"`

### 3. Assets/Scripts/UI/Scripts/GameplayUI.cs
- Обновлены ссылки на `CharacterDeckUI`
- Правильный селектор: `root.Q<CharacterDeckUI>("CharacterDeckUI")`

## Текущее состояние
✅ Все файлы компилируются без ошибок
✅ CharacterDeckUI работает как UxmlElement
✅ Интеграция с GameplayUI функционирует

## Использование
```xml
<CharacterDeckUI name="CharacterDeckUI" style="...">
    <ui:VisualElement name="TopStack" class="deck-scroll-container top" />
    <ui:Instance template="DeckSelectedCharacterContainer" name="DeckSelectedCharacterContainer" />
    <ui:VisualElement name="BottomStack" class="deck-scroll-container bottom" />
</CharacterDeckUI>
```

```csharp
CharacterDeckUI deckUI = root.Q<CharacterDeckUI>("CharacterDeckUI");
deckUI.Init(characterTileAsset);
```
