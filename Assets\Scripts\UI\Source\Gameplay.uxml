<ui:UXML xmlns:ui="UnityEngine.UIElements" editor-extension-mode="False">
    <ui:Template name="DeckSelectedCharacterContainer" src="project://database/Assets/Scripts/UI/Source/DeckSelectedCharacterContainer.uxml?fileID=9197481963319205126&amp;guid=75f1854098eca6a44bef5117c6fe2f21&amp;type=3#DeckSelectedCharacterContainer" />
    <Style src="project://database/Assets/Scripts/UI/Styles/CharacterDeck.uss?fileID=7433441132597879392&amp;guid=bb96079b731b72a4480be164e391518e&amp;type=3#CharacterDeck" />
    <ui:VisualElement name="BackgroundFrame" style="background-image: url(&quot;project://database/Assets/Art-UI%20Elements/frame.png?fileID=2800000&amp;guid=408b6c62a2ea3e34dad65e57f7bed77f&amp;type=3#frame&quot;); position: absolute; top: 0; right: 0; bottom: 0; left: 0;" />
    <CharacterDeckUI name="CharacterDeckUI" style="flex-grow: 1; align-content: stretch; align-items: flex-start; justify-content: center; align-self: flex-start; padding-top: 37px; padding-right: 13px; padding-bottom: 20px; padding-left: 20px; display: flex;">
        <ui:VisualElement name="TopStack" class="deck-scroll-container top" />
        <ui:Instance template="DeckSelectedCharacterContainer" name="DeckSelectedCharacterContainer" />
        <ui:VisualElement name="BottomStack" class="deck-scroll-container bottom" />
    </CharacterDeckUI>
</ui:UXML>
